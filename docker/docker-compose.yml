version: '3.8'

services:
  n8n:
    image: docker.n8n.io/n8nio/n8n
    container_name: n8n
    restart: unless-stopped
    ports:
      - "0.0.0.0:5678:5678"  
    environment:
      - GENERIC_TIMEZONE=Asia/<PERSON>_<PERSON>_<PERSON>
      - TZ=Asia/<PERSON>_<PERSON>_Minh
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true
      - WEBHOOK_URL=https://n8n.ekoios.net/
      - N8N_TRUST_PROXY=true  # This is crucial!
    volumes:
      - n8n_data:/home/<USER>/.n8n

volumes:
  n8n_data:
    external: true