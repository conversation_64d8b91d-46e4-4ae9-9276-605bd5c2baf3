version: '3.8'

services:
  n8n:
    image: docker.n8n.io/n8nio/n8n
    container_name: n8n
    restart: unless-stopped
    ports:
      - "0.0.0.0:5678:5678"  
    environment:
      - GENERIC_TIMEZONE=Asia/Ho_Chi_Minh
      - TZ=Asia/Ho_Chi_Minh
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true	
      - WEBHOOK_URL=https://n8n.ekoios.net/
      - N8N_TRUST_PROXY=true
      # Queue mode configuration
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=host.docker.internal
      - QUEUE_BULL_REDIS_PORT=6379
      - QUEUE_BULL_REDIS_DB=1
      - QUEUE_BULL_REDIS_PASSWORD=infini_rag_flow
      - N8N_ENCRYPTION_KEY=jkAieUC2989BY8ws9vA3rXd4uVl2KAaU
      # Database configuration (recommended for queue mode)
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=123456
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # PostgreSQL database (required for queue mode)
  postgres:
    image: postgres:15-alpine
    container_name: n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=123456
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Worker instance 1
  n8n-worker-1:
    image: docker.n8n.io/n8nio/n8n
    container_name: n8n-worker-1
    restart: unless-stopped
    command: ["worker", "--concurrency=10"]
    environment:
      - GENERIC_TIMEZONE=Asia/Ho_Chi_Minh
      - TZ=Asia/Ho_Chi_Minh
      # Queue mode configuration (same as main)
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=host.docker.internal
      - QUEUE_BULL_REDIS_PORT=6379
      - QUEUE_BULL_REDIS_DB=1
      - QUEUE_BULL_REDIS_PASSWORD=infini_rag_flow
      - N8N_ENCRYPTION_KEY=jkAieUC2989BY8ws9vA3rXd4uVl2KAaU
      # Database configuration (same as main)
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=123456
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Worker instance 2 (optional - add more if needed)
  n8n-worker-2:
    image: docker.n8n.io/n8nio/n8n
    container_name: n8n-worker-2
    restart: unless-stopped
    command: ["worker", "--concurrency=10"]
    environment:
      - GENERIC_TIMEZONE=Asia/Ho_Chi_Minh
      - TZ=Asia/Ho_Chi_Minh
      # Queue mode configuration (same as main)
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=host.docker.internal
      - QUEUE_BULL_REDIS_PORT=6379
      - QUEUE_BULL_REDIS_DB=1
      - QUEUE_BULL_REDIS_PASSWORD=infini_rag_flow
      - N8N_ENCRYPTION_KEY=jkAieUC2989BY8ws9vA3rXd4uVl2KAaU
      # Database configuration (same as main)
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=123456
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  n8n_data:
    external: true
  postgres_data:
