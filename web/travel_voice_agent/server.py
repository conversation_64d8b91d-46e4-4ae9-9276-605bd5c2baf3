#!/usr/bin/env python3
"""
Simple HTTPS server for the Travel Voice AI Agent POC
Generates self-signed certificate for secure context
"""

import http.server
import ssl
import socketserver
import os
import subprocess
import sys
from pathlib import Path

def generate_self_signed_cert():
    """Generate a self-signed certificate for HTTPS"""
    cert_file = "server.crt"
    key_file = "server.key"
    
    if os.path.exists(cert_file) and os.path.exists(key_file):
        print("✓ Certificate files already exist")
        return cert_file, key_file
    
    print("🔐 Generating self-signed certificate...")
    
    # Generate private key and certificate
    cmd = [
        "openssl", "req", "-x509", "-newkey", "rsa:4096", 
        "-keyout", key_file, "-out", cert_file, "-days", "365", "-nodes",
        "-subj", "/C=US/ST=State/L=City/O=Organization/CN=**********"
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print("✓ Certificate generated successfully")
        return cert_file, key_file
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to generate certificate: {e}")
        print("Make sure OpenSSL is installed: sudo apt-get install openssl")
        return None, None
    except FileNotFoundError:
        print("❌ OpenSSL not found. Please install it:")
        print("Ubuntu/Debian: sudo apt-get install openssl")
        print("CentOS/RHEL: sudo yum install openssl")
        return None, None

def start_https_server(port=8443):
    """Start HTTPS server"""
    cert_file, key_file = generate_self_signed_cert()
    
    if not cert_file or not key_file:
        print("❌ Cannot start HTTPS server without certificates")
        return False
    
    # Create HTTP request handler
    handler = http.server.SimpleHTTPRequestHandler
    
    # Create server
    with socketserver.TCPServer(("0.0.0.0", port), handler) as httpd:
        # Wrap with SSL
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_file, key_file)
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        
        print(f"🚀 HTTPS Server started!")
        print(f"📱 Local access: https://localhost:{port}")
        print(f"🌐 Network access: https://**********:{port}")
        print(f"⚠️  You'll need to accept the self-signed certificate warning")
        print(f"🛑 Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
            return True

def start_http_server(port=8001):
    """Fallback HTTP server"""
    handler = http.server.SimpleHTTPRequestHandler
    
    with socketserver.TCPServer(("0.0.0.0", port), handler) as httpd:
        print(f"🚀 HTTP Server started!")
        print(f"📱 Local access (secure): http://localhost:{port}")
        print(f"🌐 Network access (insecure): http://**********:{port}")
        print(f"⚠️  Voice features only work on localhost due to security restrictions")
        print(f"🛑 Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
            return True

if __name__ == "__main__":
    print("🎙️ Travel Voice AI Agent Server")
    print("=" * 40)
    
    # Try HTTPS first, fallback to HTTP
    if len(sys.argv) > 1 and sys.argv[1] == "--http":
        start_http_server()
    else:
        print("Attempting to start HTTPS server...")
        if not start_https_server():
            print("\nFalling back to HTTP server...")
            start_http_server()
