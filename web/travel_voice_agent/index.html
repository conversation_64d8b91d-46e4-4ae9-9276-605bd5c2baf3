<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Voice AI Agent - POC</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Travel Voice AI Agent</h1>
            <p class="subtitle">Powered by ElevenLabs Voice AI</p>
        </header>

        <main class="main-content">
            <div class="agent-section">
                <div class="agent-info">
                    <h2>Voice AI Assistant</h2>
                    <p>Click the voice button below to start talking with our AI travel assistant. Ask about destinations, travel tips, or get personalized recommendations!</p>
                </div>

                <div class="agent-container">
                    <!-- ElevenLabs Voice AI Agent Widget -->
                    <elevenlabs-convai agent-id="agent_0801k55k1c5yeqw80k2gb1bdavj8"></elevenlabs-convai>
                </div>

                <div class="features">
                    <div class="feature-item">
                        <h3>🗣️ Natural Conversation</h3>
                        <p>Speak naturally with our AI agent</p>
                    </div>
                    <div class="feature-item">
                        <h3>✈️ Travel Expertise</h3>
                        <p>Get expert travel advice and recommendations</p>
                    </div>
                    <div class="feature-item">
                        <h3>🌍 Global Knowledge</h3>
                        <p>Information about destinations worldwide</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Travel Voice AI Agent POC</p>
        </footer>
    </div>

    <!-- Debug Console -->
    <div id="debug-console" style="position: fixed; bottom: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-width: 400px; max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
        <div style="margin-bottom: 5px; font-weight: bold;">Debug Log:</div>
        <div id="debug-log"></div>
        <button onclick="toggleDebug()" style="margin-top: 5px; padding: 2px 5px;">Close</button>
    </div>

    <!-- Debug Toggle Button -->
    <button onclick="toggleDebug()" style="position: fixed; bottom: 10px; right: 10px; background: #667eea; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 1001;">Debug</button>

    <!-- Debugging Script -->
    <script>
        // Debug logging function
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            if (logElement) {
                logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logElement.scrollTop = logElement.scrollHeight;
            }
            console.log(`[DEBUG] ${message}`);
        }

        // Toggle debug console
        function toggleDebug() {
            const console = document.getElementById('debug-console');
            console.style.display = console.style.display === 'none' ? 'block' : 'none';
        }

        // Check browser capabilities
        function checkBrowserCapabilities() {
            debugLog('=== Browser Capability Check ===');

            // Check if running on HTTPS or localhost
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            debugLog(`Secure context: ${isSecure}`);

            // Check getUserMedia support
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                debugLog('✓ getUserMedia is supported');
            } else if (navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia) {
                debugLog('⚠ Legacy getUserMedia detected');
            } else {
                debugLog('✗ getUserMedia not supported');
            }

            // Check microphone permissions
            if (navigator.permissions) {
                navigator.permissions.query({name: 'microphone'}).then(function(result) {
                    debugLog(`Microphone permission: ${result.state}`);
                }).catch(function(error) {
                    debugLog(`Permission check failed: ${error.message}`);
                });
            }

            debugLog(`User Agent: ${navigator.userAgent}`);
            debugLog(`Location: ${window.location.href}`);
        }

        // Monitor ElevenLabs widget loading
        function monitorElevenLabsWidget() {
            debugLog('=== ElevenLabs Widget Monitor ===');

            const widget = document.querySelector('elevenlabs-convai');
            if (widget) {
                debugLog('✓ ElevenLabs widget element found');

                // Listen for widget events
                widget.addEventListener('load', () => debugLog('Widget loaded'));
                widget.addEventListener('error', (e) => debugLog(`Widget error: ${e.message || e}`));

                // Check if widget has loaded after a delay
                setTimeout(() => {
                    if (widget.shadowRoot) {
                        debugLog('✓ Widget shadow DOM created');
                    } else {
                        debugLog('⚠ Widget shadow DOM not found');
                    }
                }, 3000);
            } else {
                debugLog('✗ ElevenLabs widget element not found');
            }
        }

        // Override console.error to catch errors
        const originalError = console.error;
        console.error = function(...args) {
            debugLog(`ERROR: ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        // Override window.onerror to catch global errors
        window.onerror = function(message, source, lineno, colno, error) {
            debugLog(`GLOBAL ERROR: ${message} at ${source}:${lineno}:${colno}`);
            return false;
        };

        // Initialize debugging when page loads
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('=== Page Loaded ===');
            checkBrowserCapabilities();

            // Wait a bit for the ElevenLabs script to load
            setTimeout(monitorElevenLabsWidget, 1000);
        });
    </script>

    <!-- ElevenLabs ConvAI Widget Script -->
    <script src="https://unpkg.com/@elevenlabs/convai-widget-embed" async type="text/javascript"></script>
</body>
</html>
