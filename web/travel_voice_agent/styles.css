/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.agent-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 100%;
    text-align: center;
}

.agent-info {
    margin-bottom: 30px;
}

.agent-info h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 15px;
}

.agent-info p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Agent container */
.agent-container {
    margin: 40px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px dashed #e9ecef;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Features section */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.feature-item {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.feature-item h3 {
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: #333;
}

.feature-item p {
    font-size: 0.9rem;
    color: #666;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .agent-section {
        padding: 25px;
    }

    .features {
        grid-template-columns: 1fr;
    }
}

/* ElevenLabs widget styling adjustments */
elevenlabs-convai {
    width: 100%;
    height: auto;
}

/* Loading state for the widget */
.agent-container:empty::before {
    content: "Loading Voice AI Agent...";
    color: #666;
    font-style: italic;
}
