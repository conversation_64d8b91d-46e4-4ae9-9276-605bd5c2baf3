# Travel Voice AI Agent - POC

A simple proof-of-concept web application that embeds Eleven<PERSON>abs Voice AI Agent for travel assistance.

## Features

- **Voice AI Integration**: Embedded ElevenLabs ConvAI widget for natural voice conversations
- **Minimal Design**: Clean, focused interface that highlights the AI agent functionality
- **Responsive Layout**: Works on desktop and mobile devices
- **Travel Focus**: Designed specifically for travel-related conversations and assistance

## Files Structure

```
web/travel_voice_agent/
├── index.html          # Main HTML file with ElevenLabs widget
├── styles.css          # CSS styling for minimal, clean design
└── README.md           # This documentation file
```

## ElevenLabs Integration

The application uses the ElevenLabs ConvAI widget with the following configuration:

- **Agent ID**: `agent_0801k55k1c5yeqw80k2gb1bdavj8`
- **Widget Script**: `https://unpkg.com/@elevenlabs/convai-widget-embed`

## Deployment

### Quick Start (Detached Mode)

To deploy the application in detached mode (background process):

```bash
# Navigate to the project directory
cd /home/<USER>/n8n/web/travel_voice_agent

# Start HTTPS server in detached mode (recommended for network access)
nohup python3 server.py > server.log 2>&1 &

# Or start HTTP server in detached mode (localhost only)
nohup python3 -m http.server 8001 > server.log 2>&1 &
```

### Access URLs

- **HTTPS (Recommended)**: `https://**********:8443` or `https://localhost:8443`
- **HTTP (Localhost only)**: `http://localhost:8001`

### Stop the Server

```bash
# Find the server process
ps aux | grep python3 | grep -E "(server.py|http.server)"

# Kill the process by PID
kill <PID>

# Or kill all Python HTTP servers (use with caution)
pkill -f "python3.*server"
```

### Check Server Status

```bash
# Check if server is running
netstat -tulpn | grep -E ":(8001|8443)"

# View server logs
tail -f server.log
```

## How to Use

1. Deploy the server using the commands above
2. Open the appropriate URL in a web browser
3. Accept the certificate warning (for HTTPS)
4. Allow microphone permissions when prompted
5. The ElevenLabs voice AI agent will load automatically
6. Click the voice button to start a conversation
7. Speak naturally about travel-related topics

## Browser Requirements

- Modern web browser with JavaScript enabled
- Microphone access permission for voice input
- Internet connection for the ElevenLabs widget

## Development

This is a static web application that can be served by any web server or opened directly in a browser. No build process or dependencies are required.

### Development Mode (Interactive)

For development and testing, you can run the server interactively:

```bash
# HTTPS server (interactive)
python3 server.py

# HTTP server (interactive)
python3 -m http.server 8001
```

Press `Ctrl+C` to stop the interactive server.

### Production Deployment

For production deployment, use the detached mode commands in the **Deployment** section above. This ensures the server continues running even after you close the terminal session.

## Customization

To customize the agent or styling:

1. **Change Agent**: Update the `agent-id` attribute in the `<elevenlabs-convai>` tag
2. **Modify Design**: Edit `styles.css` to change colors, layout, or typography
3. **Add Features**: Extend `index.html` with additional functionality as needed

## Notes

- The widget requires an active internet connection to function
- Voice features require microphone permissions in the browser
- The agent behavior is controlled by the ElevenLabs configuration for the specified agent ID
