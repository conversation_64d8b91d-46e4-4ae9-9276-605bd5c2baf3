# Travel Voice AI Agent - POC

A simple proof-of-concept web application that embeds ElevenLabs Voice AI Agent for travel assistance.

## Features

- **Voice AI Integration**: Embedded ElevenLabs ConvAI widget for natural voice conversations
- **Minimal Design**: Clean, focused interface that highlights the AI agent functionality
- **Responsive Layout**: Works on desktop and mobile devices
- **Travel Focus**: Designed specifically for travel-related conversations and assistance

## Files Structure

```
web/travel_voice_agent/
├── index.html          # Main HTML file with ElevenLabs widget
├── styles.css          # CSS styling for minimal, clean design
└── README.md           # This documentation file
```

## ElevenLabs Integration

The application uses the ElevenLabs ConvAI widget with the following configuration:

- **Agent ID**: `agent_0801k55k1c5yeqw80k2gb1bdavj8`
- **Widget Script**: `https://unpkg.com/@elevenlabs/convai-widget-embed`

## How to Use

1. Open `index.html` in a web browser
2. The ElevenLabs voice AI agent will load automatically
3. Click the voice button to start a conversation
4. Speak naturally about travel-related topics

## Browser Requirements

- Modern web browser with JavaScript enabled
- Microphone access permission for voice input
- Internet connection for the ElevenLabs widget

## Development

This is a static web application that can be served by any web server or opened directly in a browser. No build process or dependencies are required.

## Customization

To customize the agent or styling:

1. **Change Agent**: Update the `agent-id` attribute in the `<elevenlabs-convai>` tag
2. **Modify Design**: Edit `styles.css` to change colors, layout, or typography
3. **Add Features**: Extend `index.html` with additional functionality as needed

## Notes

- The widget requires an active internet connection to function
- Voice features require microphone permissions in the browser
- The agent behavior is controlled by the ElevenLabs configuration for the specified agent ID
